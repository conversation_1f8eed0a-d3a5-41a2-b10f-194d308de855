D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f CMakeFiles\testsocket.dir/objects.a
D:\AAVM\software\mingw64\bin\ar.exe qc CMakeFiles\testsocket.dir/objects.a @CMakeFiles\testsocket.dir\objects1.rsp
D:\AAVM\software\mingw64\bin\g++.exe  /W3 -g -Wl,--whole-archive CMakeFiles\testsocket.dir/objects.a -Wl,--no-whole-archive -o bin\testsocket.exe -Wl,--out-implib,libtestsocket.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\testsocket.dir\linkLibs.rsp
