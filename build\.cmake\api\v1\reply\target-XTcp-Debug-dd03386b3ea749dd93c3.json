{"archive": {}, "artifacts": [{"path": "lib/libXTcp.a"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "install", "add_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 29, "parent": 0}, {"command": 1, "file": 0, "line": 68, "parent": 0}, {"command": 2, "file": 0, "line": 13, "parent": 0}, {"command": 3, "file": 0, "line": 26, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -Wall -Wextra -g -std=gnu++11"}], "defines": [{"backtrace": 3, "define": "WIN32"}], "includes": [{"backtrace": 4, "path": "D:/AAVM/Http_Server/Test"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "id": "XTcp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "lib"}], "prefix": {"path": "C:/Program Files (x86)/Http_Server"}}, "name": "XTcp", "nameOnDisk": "libXTcp.a", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "Test/XTcp.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Test/XTcp.h", "sourceGroupIndex": 1}], "type": "STATIC_LIBRARY"}