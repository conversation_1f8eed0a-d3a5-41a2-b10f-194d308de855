#include <windows.h>
#include <iostream>
#include <string.h>
#include <thread>
#include "XTcp.h"
using namespace std;

class TcpThread {
public:
void main(){
  char buf[1024]= {0};
  while (1)
{
    memset(buf, 0, sizeof(buf)); // 清空缓冲区
    int recvlen = client.Recv(buf,sizeof(buf)-1);

    if(recvlen <=0)break;

    buf[recvlen] = '\0'; // 确保字符串结束
    cout << "recv :"<< buf << endl;

    if (strstr(buf,"quit") != NULL)
    {
      char re[] = "quit success!\n";
      int senlen = client.Send(re,strlen(re)+1);
      break;
    }

    int senlen = client.Send("ok",2);

}
 client.Close();
 delete this;
}
XTcp client;

};



int main (int argc,char *argv[]){
unsigned short port =8080;
if (argc >1)
{
    port = atoi (argv[1]);
}

XTcp server;
server.CreateSocket();
server.Bind(port);

while (1)
{
XTcp client = server.Accept();
TcpThread *th =new TcpThread();
th->client = client;
thread sth(&TcpThread::main,th);
sth.detach();

}


 server.Close();
  WSACleanup();
return 0;
}