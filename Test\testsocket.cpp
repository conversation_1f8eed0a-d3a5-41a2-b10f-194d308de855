#include <windows.h>
#include <iostream>
#include <string.h>
#include <thread>
using namespace std;

class TcpThread {
public:
void main(){
  char buf[1024]= {0};
  while (1)
{
    memset(buf, 0, sizeof(buf)); // 清空缓冲区
    int recvlen = recv(client,buf,sizeof(buf)-1,0);

    if(recvlen <=0)break;

    buf[recvlen] = '\0'; // 确保字符串结束
    cout << "recv :"<< buf << endl;

    if (strstr(buf,"quit") != NULL)
    {
      char re[] = "quit success!\n";
      int senlen = send(client,re,strlen(re),0);
      break;
    }

    int senlen = send(client,"ok",2,0);

}
 closesocket(client);
 delete this;
}
int client = 0;

};



int main (int argc,char *argv[]){

int sock = socket(AF_INET,SOCK_STREAM,0);
if (sock <= 0)
{
    cout << "socket creat failed"<<endl;
    WSACleanup();
    return -1;
}

cout << sock << endl;

unsigned short port =8080;
if (argc >1)
{
    port = atoi (argv[1]);
}


sockaddr_in saddr;
saddr.sin_family = AF_INET;
saddr.sin_port = htons(port);
saddr.sin_addr.s_addr = htonl(0);
if(bind(sock,(sockaddr*)&saddr,sizeof(saddr))!=0){
    cout << "bind port " << port << " failed"<<endl;
    closesocket(sock);
    WSACleanup();
    return -2;
}
cout << "bind port " << port << " success"<<endl;

listen(sock,10);

while (1)
{

sockaddr_in caddr;
int len = sizeof(caddr);
int client = accept(sock,(sockaddr*)&caddr,&len);
cout<<"accept client:" << client << endl ;
char *ip = inet_ntoa(caddr.sin_addr);
unsigned short cport = ntohs(caddr.sin_port);
cout << "ip is "<<ip << "port is "<< cport <<endl;

TcpThread *th =new TcpThread();
th->client = client;
thread sth(&TcpThread::main,th);
sth.detach();

}


  closesocket(sock);
  WSACleanup();
return 0;
}