#include <windows.h>
#include <iostream>
using namespace std;
int main (int argc,char *argv[]){
WSADATA ws;
WSAStartup(MAKEWORD(2,2),&ws);
int sock = socket(AF_INET,SOCK_STREAM,0);
if (sock <= 0)
{
    cout << "socket creat failed"<<endl;
}

cout << sock << endl;

unsigned short port =80;
if (argc >1)
{
    port = atoi (argv[1]);
}
WSACleanup();
sockaddr_in saddr;
saddr.sin_family = AF_INET;
saddr.sin_port = htons(port);
saddr.sin_addr.s_addr = htonl(0);
if(bind(sock,(sockaddr*)&saddr,sizeof(saddr))!=0){
    cout << "bind port " << port << "failed"<<endl;
    return -2;
}
cout << "bind "
return 0;
}