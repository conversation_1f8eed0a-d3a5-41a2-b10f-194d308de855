#include <windows.h>
#include <iostream>
#include <string.h>
using namespace std;
int main (int argc,char *argv[]){
WSADATA ws;
WSAStartup(MAKEWORD(2,2),&ws);
int sock = socket(AF_INET,SOCK_STREAM,0);
if (sock <= 0)
{
    cout << "socket creat failed"<<endl;
    WSACleanup();
    return -1;
}

cout << sock << endl;

unsigned short port =8080;
if (argc >1)
{
    port = atoi (argv[1]);
}


sockaddr_in saddr;
saddr.sin_family = AF_INET;
saddr.sin_port = htons(port);
saddr.sin_addr.s_addr = htonl(0);
if(bind(sock,(sockaddr*)&saddr,sizeof(saddr))!=0){
    cout << "bind port " << port << " failed"<<endl;
    closesocket(sock);
    WSACleanup();
    return -2;
}
  cout << "bind port " << port << " success"<<endl;

  listen(sock,10);

sockaddr_in caddr;
int len = sizeof(caddr);
int client = accept(sock,(sockaddr*)&caddr,&len);
cout<<"accept client:" << client << endl ;
char *ip = inet_ntoa(caddr.sin_addr);
unsigned short cport = ntohs(caddr.sin_port);
cout << "ip is "<<ip << "port is "<< cport <<endl;
char buf[1024]= {0};
while (1)
{
    int recvlen = recv(client,buf,sizeof(buf)-1,0);

    if(recvlen <=0)
 
}


cout << "recv :"<< buf << endl;

  closesocket(sock);
  closesocket(client);
  WSACleanup();
return 0;
}