cmake_minimum_required(VERSION 3.10)

# 项目名称
project(Http_Server)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译器标志
if(WIN32)
    # Windows 特定设置
    add_definitions(-DWIN32)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")
else()
    # Linux/Unix 特定设置
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
endif()

# 包含目录
include_directories(Test)

# XTcp 库
add_library(XTcp
    Test/XTcp.cpp
    Test/XTcp.h
)

# 链接库设置
if(WIN32)
    # Windows 需要链接 Winsock 库
    target_link_libraries(XTcp ws2_32)
else()
    # Linux 需要链接 pthread 库
    find_package(Threads REQUIRED)
    target_link_libraries(XTcp Threads::Threads)
endif()

# testsocket 可执行文件
add_executable(testsocket
    Test/testsocket.cpp
)

# 链接库
target_link_libraries(testsocket XTcp)
if(WIN32)
    target_link_libraries(testsocket ws2_32)
else()
    target_link_libraries(testsocket Threads::Threads)
endif()

# 设置输出目录
set_target_properties(testsocket PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

set_target_properties(XTcp PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# 安装规则
install(TARGETS testsocket XTcp
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(FILES Test/XTcp.h
    DESTINATION include
)