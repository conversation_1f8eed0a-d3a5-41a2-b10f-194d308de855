{"artifacts": [{"path": "bin/testsocket.exe"}, {"path": "bin/testsocket.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries", "add_definitions", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 40, "parent": 0}, {"command": 1, "file": 0, "line": 63, "parent": 0}, {"command": 2, "file": 0, "line": 45, "parent": 0}, {"command": 2, "file": 0, "line": 47, "parent": 0}, {"command": 3, "file": 0, "line": 13, "parent": 0}, {"command": 4, "file": 0, "line": 21, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " /W3 -g -std=gnu++11"}], "defines": [{"backtrace": 5, "define": "WIN32"}], "includes": [{"backtrace": 6, "path": "D:/AAVM/Http_Server/Test"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0]}], "dependencies": [{"backtrace": 3, "id": "XTcp::@6890427a1f51a3e7e1df"}], "id": "testsocket::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "C:/Program Files (x86)/Http_Server"}}, "link": {"commandFragments": [{"fragment": "/W3 -g", "role": "flags"}, {"backtrace": 3, "fragment": "lib\\libXTcp.a", "role": "libraries"}, {"backtrace": 4, "fragment": "-lws2_32", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "testsocket", "nameOnDisk": "testsocket.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "Test/testsocket.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}