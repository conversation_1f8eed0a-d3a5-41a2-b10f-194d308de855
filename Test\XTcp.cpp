#include "XTcp.h"

#include <iostream>
#include <string.h>
#include <thread>

#ifdef WIN32
#include <windows.h>
#define socklen_t int
#else
#include <sys/types.h>
#include <sys/socket.h>
#include <unistd.h>
#include <arpa/inet.h>
#define closesocket close;
#endif


using namespace std;

XTcp::XTcp(){
#ifdef WIN32
static bool first = true;
if (first)
{
    first = false;
  WSADATA ws;
WSAStartup(MAKEWORD(2,2),&ws);
}
#endif
}

bool XTcp::Bind(unsigned short port){
  int sock = socket(AF_INET,SOCK_STREAM,0);
if (sock <= 0)
{
    cout << "socket creat failed"<<endl;
    WSACleanup();
    return -1;
}

cout << sock << endl;

sockaddr_in saddr;
saddr.sin_family = AF_INET;
saddr.sin_port = htons(port);
saddr.sin_addr.s_addr = htonl(0);
if(bind(sock,(sockaddr*)&saddr,sizeof(saddr))!=0){
    cout << "bind port " << port << " failed"<<endl;
    closesocket(sock);
    WSACleanup();
    return false;
}
cout << "bind port " << port << " success"<<endl;

listen(sock,10);

return true;
}

XTcp XTcp::Accept(){
  XTcp tcp;
  sockaddr_in caddr;
int len = sizeof(caddr);
int client = accept(sock,(sockaddr*)&caddr,&len);
if(client <= 0)return tcp;
cout<<"accept client:" << client << endl ;
tcp.ip = inet_ntoa(caddr.sin_addr);
tcp.port = ntohs(caddr.sin_port);
tcp.sock = client;
cout << "ip is "<< tcp.ip.c_str() << "port is "<< tcp.port <<endl;
return tcp;
}

void XTcp::Close(){
  if (sock <= 0) return;
closesocket(sock);
}

int XTcp::Recv(char *buf,int bufsize){
return recv(sock,buf,bufsize,0);
}

int XTcp::Send(const char *buf,int size){
int s = 0;

while (s != size)
{
  int len = send (sock,buf+s,size - s,0);
  if (len <= 0)break;
  
  s += len;
  
}
return s;
}

XTcp::~XTcp(){}