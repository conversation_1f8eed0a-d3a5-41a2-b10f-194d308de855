# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\C++source\Tools\Cmake\bin\cmake.exe

# The command to remove a file.
RM = D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\AAVM\Http_Server

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\AAVM\Http_Server\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	D:\C++source\Tools\Cmake\bin\cmake-gui.exe -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	D:\C++source\Tools\Cmake\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	D:\C++source\Tools\Cmake\bin\cmake.exe -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	D:\C++source\Tools\Cmake\bin\cmake.exe -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	D:\C++source\Tools\Cmake\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	D:\C++source\Tools\Cmake\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	D:\C++source\Tools\Cmake\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	D:\C++source\Tools\Cmake\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\AAVM\Http_Server\build\CMakeFiles D:\AAVM\Http_Server\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\AAVM\Http_Server\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named XTcp

# Build rule for target.
XTcp: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 XTcp
.PHONY : XTcp

# fast build rule for target.
XTcp/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\XTcp.dir\build.make CMakeFiles/XTcp.dir/build
.PHONY : XTcp/fast

#=============================================================================
# Target rules for targets named testsocket

# Build rule for target.
testsocket: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 testsocket
.PHONY : testsocket

# fast build rule for target.
testsocket/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\testsocket.dir\build.make CMakeFiles/testsocket.dir/build
.PHONY : testsocket/fast

Test/XTcp.obj: Test/XTcp.cpp.obj
.PHONY : Test/XTcp.obj

# target to build an object file
Test/XTcp.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\XTcp.dir\build.make CMakeFiles/XTcp.dir/Test/XTcp.cpp.obj
.PHONY : Test/XTcp.cpp.obj

Test/XTcp.i: Test/XTcp.cpp.i
.PHONY : Test/XTcp.i

# target to preprocess a source file
Test/XTcp.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\XTcp.dir\build.make CMakeFiles/XTcp.dir/Test/XTcp.cpp.i
.PHONY : Test/XTcp.cpp.i

Test/XTcp.s: Test/XTcp.cpp.s
.PHONY : Test/XTcp.s

# target to generate assembly for a file
Test/XTcp.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\XTcp.dir\build.make CMakeFiles/XTcp.dir/Test/XTcp.cpp.s
.PHONY : Test/XTcp.cpp.s

Test/testsocket.obj: Test/testsocket.cpp.obj
.PHONY : Test/testsocket.obj

# target to build an object file
Test/testsocket.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\testsocket.dir\build.make CMakeFiles/testsocket.dir/Test/testsocket.cpp.obj
.PHONY : Test/testsocket.cpp.obj

Test/testsocket.i: Test/testsocket.cpp.i
.PHONY : Test/testsocket.i

# target to preprocess a source file
Test/testsocket.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\testsocket.dir\build.make CMakeFiles/testsocket.dir/Test/testsocket.cpp.i
.PHONY : Test/testsocket.cpp.i

Test/testsocket.s: Test/testsocket.cpp.s
.PHONY : Test/testsocket.s

# target to generate assembly for a file
Test/testsocket.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\testsocket.dir\build.make CMakeFiles/testsocket.dir/Test/testsocket.cpp.s
.PHONY : Test/testsocket.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... install
	@echo ... install/local
	@echo ... install/strip
	@echo ... list_install_components
	@echo ... rebuild_cache
	@echo ... XTcp
	@echo ... testsocket
	@echo ... Test/XTcp.obj
	@echo ... Test/XTcp.i
	@echo ... Test/XTcp.s
	@echo ... Test/testsocket.obj
	@echo ... Test/testsocket.i
	@echo ... Test/testsocket.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

