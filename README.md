# Http_Server 项目

这是一个基于 C++ 的 TCP 服务器项目，支持多客户端连接。

## 项目结构

```
Http_Server/
├── CMakeLists.txt      # CMake 构建配置文件
├── build.bat           # Windows 构建脚本
├── build.sh            # Linux 构建脚本
├── README.md           # 项目说明文档
└── Test/
    ├── XTcp.h          # TCP 封装类头文件
    ├── XTcp.cpp        # TCP 封装类实现
    └── testsocket.cpp  # 测试服务器程序
```

## 功能特性

- 跨平台支持 (Windows/Linux)
- 多线程处理客户端连接
- 简单的消息回显功能
- 支持 "quit" 命令优雅断开连接

## 构建要求

### Windows
- CMake 3.10 或更高版本
- MinGW 或 Visual Studio
- Windows SDK (包含 Winsock2)

### Linux
- CMake 3.10 或更高版本
- GCC 或 Clang
- pthread 库

## 构建方法

### Windows
```bash
# 使用构建脚本
build.bat

# 或手动构建
mkdir build
cd build
cmake .. -G "MinGW Makefiles"
cmake --build .
```

### Linux
```bash
# 使用构建脚本
./build.sh

# 或手动构建
mkdir build
cd build
cmake ..
cmake --build .
```

## 运行

构建完成后，可执行文件位于 `build/bin/` 目录下：

```bash
# Windows
build\bin\testsocket.exe [port]

# Linux
build/bin/testsocket [port]
```

默认端口为 8080，也可以指定其他端口。

## 测试

启动服务器后，可以使用 telnet 进行测试：

```bash
telnet localhost 8080
```

- 发送任意消息，服务器会回复 "ok"
- 发送包含 "quit" 的消息，服务器会回复 "quit success!" 并断开连接

## 示例

```bash
# 启动服务器
./build/bin/testsocket 8080

# 在另一个终端连接
telnet localhost 8080
# 输入: hello
# 服务器回复: ok
# 输入: quit
# 服务器回复: quit success!
# 连接断开
```
