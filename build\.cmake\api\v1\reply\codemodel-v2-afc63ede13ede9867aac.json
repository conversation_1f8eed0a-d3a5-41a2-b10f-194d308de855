{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-7e71ae6476da0890454a.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Http_Server", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "XTcp::@6890427a1f51a3e7e1df", "jsonFile": "target-XTcp-Debug-dd03386b3ea749dd93c3.json", "name": "XTcp", "projectIndex": 0}, {"directoryIndex": 0, "id": "testsocket::@6890427a1f51a3e7e1df", "jsonFile": "target-testsocket-Debug-7604ef9922c7d06c82f2.json", "name": "testsocket", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/AAVM/Http_Server/build", "source": "D:/AAVM/Http_Server"}, "version": {"major": 2, "minor": 8}}