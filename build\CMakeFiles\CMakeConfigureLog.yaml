
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake:212 (message)"
      - "CMakeLists.txt:4 (project)"
    message: |
      The system is: Windows - 10.0.22631 - AMD64
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeMinGWFindMake.cmake:5 (find_program)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_MAKE_PROGRAM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "mingw32-make.exe"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
      - "/REGISTRY-NOTFOUND/bin/"
      - "c:/MinGW/bin/"
      - "/MinGW/bin/"
      - "/REGISTRY-NOTFOUND/MinGW/bin/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/mingw32-make.exe.com"
    found: "D:/AAVM/software/mingw64/bin/mingw32-make.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCCompilerId.c.in"
    candidate_directories:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/"
    found: "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCCompilerId.c.in"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:122 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: D:/AAVM/software/mingw64/bin/gcc.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is GNU, found in:
        D:/AAVM/Http_Server/build/CMakeFiles/4.1.0/CompilerIdC/a.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_AR"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ar"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/ar.com"
    found: "D:/AAVM/software/mingw64/bin/ar.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_RANLIB"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ranlib"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/ranlib.com"
    found: "D:/AAVM/software/mingw64/bin/ranlib.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_STRIP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "strip"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/strip.com"
    found: "D:/AAVM/software/mingw64/bin/strip.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_LINKER"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "ld"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/ld.com"
    found: "D:/AAVM/software/mingw64/bin/ld.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_NM"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "nm"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/nm.com"
    found: "D:/AAVM/software/mingw64/bin/nm.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_OBJDUMP"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objdump"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/objdump.com"
    found: "D:/AAVM/software/mingw64/bin/objdump.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_OBJCOPY"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "objcopy"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/objcopy.com"
    found: "D:/AAVM/software/mingw64/bin/objcopy.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_READELF"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "readelf"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/readelf.com"
    found: "D:/AAVM/software/mingw64/bin/readelf.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_DLLTOOL"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "dlltool"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/dlltool.com"
    found: "D:/AAVM/software/mingw64/bin/dlltool.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_ADDR2LINE"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "addr2line"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/addr2line.com"
    found: "D:/AAVM/software/mingw64/bin/addr2line.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake:238 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:200 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_TAPI"
    description: "Path to a program."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "tapi"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/tapi.com"
      - "D:/AAVM/software/mingw64/bin/tapi.exe"
      - "D:/AAVM/software/mingw64/bin/tapi"
      - "D:/AAVM/software/usr/bin/tapi.com"
      - "D:/AAVM/software/usr/bin/tapi.exe"
      - "D:/AAVM/software/usr/bin/tapi"
      - "D:/visal/bin/tapi.com"
      - "D:/visal/bin/tapi.exe"
      - "D:/visal/bin/tapi"
      - "C:/Windows/System32/tapi.com"
      - "C:/Windows/System32/tapi.exe"
      - "C:/Windows/System32/tapi"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/tapi.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/tapi.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/tapi"
      - "D:/jdk/jre/bin/tapi.com"
      - "D:/jdk/jre/bin/tapi.exe"
      - "D:/jdk/jre/bin/tapi"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/tapi.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/tapi.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/tapi"
      - "C:/Windows/System32/HWAudioDriver/tapi.com"
      - "C:/Windows/System32/HWAudioDriver/tapi.exe"
      - "C:/Windows/System32/HWAudioDriver/tapi"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/tapi.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/tapi.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/tapi"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/tapi.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/tapi.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/tapi"
      - "D:/Program Files/Nox/bin/tapi.com"
      - "D:/Program Files/Nox/bin/tapi.exe"
      - "D:/Program Files/Nox/bin/tapi"
      - "D:/sdk/platform-tools/tapi.com"
      - "D:/sdk/platform-tools/tapi.exe"
      - "D:/sdk/platform-tools/tapi"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/tapi.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/tapi.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/tapi"
      - "C:/Program Files/dotnet/tapi.com"
      - "C:/Program Files/dotnet/tapi.exe"
      - "C:/Program Files/dotnet/tapi"
      - "C:/Windows/tapi.com"
      - "C:/Windows/tapi.exe"
      - "C:/Windows/tapi"
      - "C:/Windows/System32/OpenSSH/tapi.com"
      - "C:/Windows/System32/OpenSSH/tapi.exe"
      - "C:/Windows/System32/OpenSSH/tapi"
      - "C:/Windows/System32/wbem/tapi.com"
      - "C:/Windows/System32/wbem/tapi.exe"
      - "C:/Windows/System32/wbem/tapi"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/tapi"
      - "D:/nodejs/tapi.com"
      - "D:/nodejs/tapi.exe"
      - "D:/nodejs/tapi"
      - "D:/nodejs/node_global/tapi.com"
      - "D:/nodejs/node_global/tapi.exe"
      - "D:/nodejs/node_global/tapi"
      - "D:/nodejs/node_cache/tapi.com"
      - "D:/nodejs/node_cache/tapi.exe"
      - "D:/nodejs/node_cache/tapi"
      - "D:/jdk/bin/tapi.com"
      - "D:/jdk/bin/tapi.exe"
      - "D:/jdk/bin/tapi"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/tapi.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/tapi.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/tapi"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/tapi"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/tapi"
      - "C:/Users/<USER>/.dotnet/tools/tapi.com"
      - "C:/Users/<USER>/.dotnet/tools/tapi.exe"
      - "C:/Users/<USER>/.dotnet/tools/tapi"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/tapi.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/tapi.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/tapi"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/tapi.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/tapi.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/tapi"
      - "D:/qiuyv123/Scripts/tapi.com"
      - "D:/qiuyv123/Scripts/tapi.exe"
      - "D:/qiuyv123/Scripts/tapi"
      - "D:/qiuyv123/tapi.com"
      - "D:/qiuyv123/tapi.exe"
      - "D:/qiuyv123/tapi"
      - "D:/qiuyv123/Library/bin/tapi.com"
      - "D:/qiuyv123/Library/bin/tapi.exe"
      - "D:/qiuyv123/Library/bin/tapi"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/tapi.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/tapi.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/tapi"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/tapi.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/tapi.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/tapi"
      - "D:/src/vcpkg/tapi.com"
      - "D:/src/vcpkg/tapi.exe"
      - "D:/src/vcpkg/tapi"
    found: false
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-15.1"
      - "gcc-ar-15"
      - "gcc-ar15"
      - "gcc-ar"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15.1.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15.1.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15.1"
      - "D:/AAVM/software/usr/bin/gcc-ar-15.1.com"
      - "D:/AAVM/software/usr/bin/gcc-ar-15.1.exe"
      - "D:/AAVM/software/usr/bin/gcc-ar-15.1"
      - "D:/visal/bin/gcc-ar-15.1.com"
      - "D:/visal/bin/gcc-ar-15.1.exe"
      - "D:/visal/bin/gcc-ar-15.1"
      - "C:/Windows/System32/gcc-ar-15.1.com"
      - "C:/Windows/System32/gcc-ar-15.1.exe"
      - "C:/Windows/System32/gcc-ar-15.1"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15.1.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15.1.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15.1"
      - "D:/jdk/jre/bin/gcc-ar-15.1.com"
      - "D:/jdk/jre/bin/gcc-ar-15.1.exe"
      - "D:/jdk/jre/bin/gcc-ar-15.1"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15.1.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15.1.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15.1"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15.1.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15.1.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15.1"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15.1.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15.1.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15.1"
      - "D:/Program Files/Nox/bin/gcc-ar-15.1.com"
      - "D:/Program Files/Nox/bin/gcc-ar-15.1.exe"
      - "D:/Program Files/Nox/bin/gcc-ar-15.1"
      - "D:/sdk/platform-tools/gcc-ar-15.1.com"
      - "D:/sdk/platform-tools/gcc-ar-15.1.exe"
      - "D:/sdk/platform-tools/gcc-ar-15.1"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15.1.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15.1.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15.1"
      - "C:/Program Files/dotnet/gcc-ar-15.1.com"
      - "C:/Program Files/dotnet/gcc-ar-15.1.exe"
      - "C:/Program Files/dotnet/gcc-ar-15.1"
      - "C:/Windows/gcc-ar-15.1.com"
      - "C:/Windows/gcc-ar-15.1.exe"
      - "C:/Windows/gcc-ar-15.1"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.1.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.1.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.1"
      - "C:/Windows/System32/wbem/gcc-ar-15.1.com"
      - "C:/Windows/System32/wbem/gcc-ar-15.1.exe"
      - "C:/Windows/System32/wbem/gcc-ar-15.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1"
      - "D:/nodejs/gcc-ar-15.1.com"
      - "D:/nodejs/gcc-ar-15.1.exe"
      - "D:/nodejs/gcc-ar-15.1"
      - "D:/nodejs/node_global/gcc-ar-15.1.com"
      - "D:/nodejs/node_global/gcc-ar-15.1.exe"
      - "D:/nodejs/node_global/gcc-ar-15.1"
      - "D:/nodejs/node_cache/gcc-ar-15.1.com"
      - "D:/nodejs/node_cache/gcc-ar-15.1.exe"
      - "D:/nodejs/node_cache/gcc-ar-15.1"
      - "D:/jdk/bin/gcc-ar-15.1.com"
      - "D:/jdk/bin/gcc-ar-15.1.exe"
      - "D:/jdk/bin/gcc-ar-15.1"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15.1.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15.1.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15.1"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.1.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.1.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.1"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15.1.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15.1.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15.1"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15.1.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15.1.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15.1"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15.1.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15.1.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15.1"
      - "D:/qiuyv123/Scripts/gcc-ar-15.1.com"
      - "D:/qiuyv123/Scripts/gcc-ar-15.1.exe"
      - "D:/qiuyv123/Scripts/gcc-ar-15.1"
      - "D:/qiuyv123/gcc-ar-15.1.com"
      - "D:/qiuyv123/gcc-ar-15.1.exe"
      - "D:/qiuyv123/gcc-ar-15.1"
      - "D:/qiuyv123/Library/bin/gcc-ar-15.1.com"
      - "D:/qiuyv123/Library/bin/gcc-ar-15.1.exe"
      - "D:/qiuyv123/Library/bin/gcc-ar-15.1"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15.1.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15.1.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15.1"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15.1.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15.1.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15.1"
      - "D:/src/vcpkg/gcc-ar-15.1.com"
      - "D:/src/vcpkg/gcc-ar-15.1.exe"
      - "D:/src/vcpkg/gcc-ar-15.1"
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15"
      - "D:/AAVM/software/usr/bin/gcc-ar-15.com"
      - "D:/AAVM/software/usr/bin/gcc-ar-15.exe"
      - "D:/AAVM/software/usr/bin/gcc-ar-15"
      - "D:/visal/bin/gcc-ar-15.com"
      - "D:/visal/bin/gcc-ar-15.exe"
      - "D:/visal/bin/gcc-ar-15"
      - "C:/Windows/System32/gcc-ar-15.com"
      - "C:/Windows/System32/gcc-ar-15.exe"
      - "C:/Windows/System32/gcc-ar-15"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15"
      - "D:/jdk/jre/bin/gcc-ar-15.com"
      - "D:/jdk/jre/bin/gcc-ar-15.exe"
      - "D:/jdk/jre/bin/gcc-ar-15"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15"
      - "D:/Program Files/Nox/bin/gcc-ar-15.com"
      - "D:/Program Files/Nox/bin/gcc-ar-15.exe"
      - "D:/Program Files/Nox/bin/gcc-ar-15"
      - "D:/sdk/platform-tools/gcc-ar-15.com"
      - "D:/sdk/platform-tools/gcc-ar-15.exe"
      - "D:/sdk/platform-tools/gcc-ar-15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15"
      - "C:/Program Files/dotnet/gcc-ar-15.com"
      - "C:/Program Files/dotnet/gcc-ar-15.exe"
      - "C:/Program Files/dotnet/gcc-ar-15"
      - "C:/Windows/gcc-ar-15.com"
      - "C:/Windows/gcc-ar-15.exe"
      - "C:/Windows/gcc-ar-15"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15"
      - "C:/Windows/System32/wbem/gcc-ar-15.com"
      - "C:/Windows/System32/wbem/gcc-ar-15.exe"
      - "C:/Windows/System32/wbem/gcc-ar-15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15"
      - "D:/nodejs/gcc-ar-15.com"
      - "D:/nodejs/gcc-ar-15.exe"
      - "D:/nodejs/gcc-ar-15"
      - "D:/nodejs/node_global/gcc-ar-15.com"
      - "D:/nodejs/node_global/gcc-ar-15.exe"
      - "D:/nodejs/node_global/gcc-ar-15"
      - "D:/nodejs/node_cache/gcc-ar-15.com"
      - "D:/nodejs/node_cache/gcc-ar-15.exe"
      - "D:/nodejs/node_cache/gcc-ar-15"
      - "D:/jdk/bin/gcc-ar-15.com"
      - "D:/jdk/bin/gcc-ar-15.exe"
      - "D:/jdk/bin/gcc-ar-15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15"
      - "D:/qiuyv123/Scripts/gcc-ar-15.com"
      - "D:/qiuyv123/Scripts/gcc-ar-15.exe"
      - "D:/qiuyv123/Scripts/gcc-ar-15"
      - "D:/qiuyv123/gcc-ar-15.com"
      - "D:/qiuyv123/gcc-ar-15.exe"
      - "D:/qiuyv123/gcc-ar-15"
      - "D:/qiuyv123/Library/bin/gcc-ar-15.com"
      - "D:/qiuyv123/Library/bin/gcc-ar-15.exe"
      - "D:/qiuyv123/Library/bin/gcc-ar-15"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15"
      - "D:/src/vcpkg/gcc-ar-15.com"
      - "D:/src/vcpkg/gcc-ar-15.exe"
      - "D:/src/vcpkg/gcc-ar-15"
      - "D:/AAVM/software/mingw64/bin/gcc-ar15.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ar15.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ar15"
      - "D:/AAVM/software/usr/bin/gcc-ar15.com"
      - "D:/AAVM/software/usr/bin/gcc-ar15.exe"
      - "D:/AAVM/software/usr/bin/gcc-ar15"
      - "D:/visal/bin/gcc-ar15.com"
      - "D:/visal/bin/gcc-ar15.exe"
      - "D:/visal/bin/gcc-ar15"
      - "C:/Windows/System32/gcc-ar15.com"
      - "C:/Windows/System32/gcc-ar15.exe"
      - "C:/Windows/System32/gcc-ar15"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar15.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar15.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar15"
      - "D:/jdk/jre/bin/gcc-ar15.com"
      - "D:/jdk/jre/bin/gcc-ar15.exe"
      - "D:/jdk/jre/bin/gcc-ar15"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar15.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar15.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar15"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar15.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar15.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar15"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar15.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar15.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar15"
      - "D:/Program Files/Nox/bin/gcc-ar15.com"
      - "D:/Program Files/Nox/bin/gcc-ar15.exe"
      - "D:/Program Files/Nox/bin/gcc-ar15"
      - "D:/sdk/platform-tools/gcc-ar15.com"
      - "D:/sdk/platform-tools/gcc-ar15.exe"
      - "D:/sdk/platform-tools/gcc-ar15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar15"
      - "C:/Program Files/dotnet/gcc-ar15.com"
      - "C:/Program Files/dotnet/gcc-ar15.exe"
      - "C:/Program Files/dotnet/gcc-ar15"
      - "C:/Windows/gcc-ar15.com"
      - "C:/Windows/gcc-ar15.exe"
      - "C:/Windows/gcc-ar15"
      - "C:/Windows/System32/OpenSSH/gcc-ar15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar15"
      - "C:/Windows/System32/wbem/gcc-ar15.com"
      - "C:/Windows/System32/wbem/gcc-ar15.exe"
      - "C:/Windows/System32/wbem/gcc-ar15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15"
      - "D:/nodejs/gcc-ar15.com"
      - "D:/nodejs/gcc-ar15.exe"
      - "D:/nodejs/gcc-ar15"
      - "D:/nodejs/node_global/gcc-ar15.com"
      - "D:/nodejs/node_global/gcc-ar15.exe"
      - "D:/nodejs/node_global/gcc-ar15"
      - "D:/nodejs/node_cache/gcc-ar15.com"
      - "D:/nodejs/node_cache/gcc-ar15.exe"
      - "D:/nodejs/node_cache/gcc-ar15"
      - "D:/jdk/bin/gcc-ar15.com"
      - "D:/jdk/bin/gcc-ar15.exe"
      - "D:/jdk/bin/gcc-ar15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar15.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar15.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar15"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar15.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar15.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar15"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar15.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar15.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar15"
      - "D:/qiuyv123/Scripts/gcc-ar15.com"
      - "D:/qiuyv123/Scripts/gcc-ar15.exe"
      - "D:/qiuyv123/Scripts/gcc-ar15"
      - "D:/qiuyv123/gcc-ar15.com"
      - "D:/qiuyv123/gcc-ar15.exe"
      - "D:/qiuyv123/gcc-ar15"
      - "D:/qiuyv123/Library/bin/gcc-ar15.com"
      - "D:/qiuyv123/Library/bin/gcc-ar15.exe"
      - "D:/qiuyv123/Library/bin/gcc-ar15"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar15.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar15.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar15"
      - "D:/src/vcpkg/gcc-ar15.com"
      - "D:/src/vcpkg/gcc-ar15.exe"
      - "D:/src/vcpkg/gcc-ar15"
      - "D:/AAVM/software/mingw64/bin/gcc-ar.com"
    found: "D:/AAVM/software/mingw64/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCCompiler.cmake:201 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_C_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-15.1"
      - "gcc-ranlib-15"
      - "gcc-ranlib15"
      - "gcc-ranlib"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15.1.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15.1.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15.1"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15.1.com"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15.1.exe"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15.1"
      - "D:/visal/bin/gcc-ranlib-15.1.com"
      - "D:/visal/bin/gcc-ranlib-15.1.exe"
      - "D:/visal/bin/gcc-ranlib-15.1"
      - "C:/Windows/System32/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/gcc-ranlib-15.1"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15.1.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15.1.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15.1"
      - "D:/jdk/jre/bin/gcc-ranlib-15.1.com"
      - "D:/jdk/jre/bin/gcc-ranlib-15.1.exe"
      - "D:/jdk/jre/bin/gcc-ranlib-15.1"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15.1"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15.1.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15.1.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15.1"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15.1.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15.1.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15.1"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15.1.com"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15.1.exe"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15.1"
      - "D:/sdk/platform-tools/gcc-ranlib-15.1.com"
      - "D:/sdk/platform-tools/gcc-ranlib-15.1.exe"
      - "D:/sdk/platform-tools/gcc-ranlib-15.1"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15.1.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15.1.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15.1"
      - "C:/Program Files/dotnet/gcc-ranlib-15.1.com"
      - "C:/Program Files/dotnet/gcc-ranlib-15.1.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-15.1"
      - "C:/Windows/gcc-ranlib-15.1.com"
      - "C:/Windows/gcc-ranlib-15.1.exe"
      - "C:/Windows/gcc-ranlib-15.1"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.1"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1"
      - "D:/nodejs/gcc-ranlib-15.1.com"
      - "D:/nodejs/gcc-ranlib-15.1.exe"
      - "D:/nodejs/gcc-ranlib-15.1"
      - "D:/nodejs/node_global/gcc-ranlib-15.1.com"
      - "D:/nodejs/node_global/gcc-ranlib-15.1.exe"
      - "D:/nodejs/node_global/gcc-ranlib-15.1"
      - "D:/nodejs/node_cache/gcc-ranlib-15.1.com"
      - "D:/nodejs/node_cache/gcc-ranlib-15.1.exe"
      - "D:/nodejs/node_cache/gcc-ranlib-15.1"
      - "D:/jdk/bin/gcc-ranlib-15.1.com"
      - "D:/jdk/bin/gcc-ranlib-15.1.exe"
      - "D:/jdk/bin/gcc-ranlib-15.1"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15.1.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15.1.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15.1"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.1.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.1.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.1"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15.1.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15.1.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15.1"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15.1.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15.1.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15.1"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15.1.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15.1.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15.1"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15.1.com"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15.1.exe"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15.1"
      - "D:/qiuyv123/gcc-ranlib-15.1.com"
      - "D:/qiuyv123/gcc-ranlib-15.1.exe"
      - "D:/qiuyv123/gcc-ranlib-15.1"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15.1.com"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15.1.exe"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15.1"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15.1.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15.1.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15.1"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15.1.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15.1.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15.1"
      - "D:/src/vcpkg/gcc-ranlib-15.1.com"
      - "D:/src/vcpkg/gcc-ranlib-15.1.exe"
      - "D:/src/vcpkg/gcc-ranlib-15.1"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15.com"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15.exe"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15"
      - "D:/visal/bin/gcc-ranlib-15.com"
      - "D:/visal/bin/gcc-ranlib-15.exe"
      - "D:/visal/bin/gcc-ranlib-15"
      - "C:/Windows/System32/gcc-ranlib-15.com"
      - "C:/Windows/System32/gcc-ranlib-15.exe"
      - "C:/Windows/System32/gcc-ranlib-15"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15"
      - "D:/jdk/jre/bin/gcc-ranlib-15.com"
      - "D:/jdk/jre/bin/gcc-ranlib-15.exe"
      - "D:/jdk/jre/bin/gcc-ranlib-15"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15.com"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15.exe"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15"
      - "D:/sdk/platform-tools/gcc-ranlib-15.com"
      - "D:/sdk/platform-tools/gcc-ranlib-15.exe"
      - "D:/sdk/platform-tools/gcc-ranlib-15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15"
      - "C:/Program Files/dotnet/gcc-ranlib-15.com"
      - "C:/Program Files/dotnet/gcc-ranlib-15.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-15"
      - "C:/Windows/gcc-ranlib-15.com"
      - "C:/Windows/gcc-ranlib-15.exe"
      - "C:/Windows/gcc-ranlib-15"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15"
      - "D:/nodejs/gcc-ranlib-15.com"
      - "D:/nodejs/gcc-ranlib-15.exe"
      - "D:/nodejs/gcc-ranlib-15"
      - "D:/nodejs/node_global/gcc-ranlib-15.com"
      - "D:/nodejs/node_global/gcc-ranlib-15.exe"
      - "D:/nodejs/node_global/gcc-ranlib-15"
      - "D:/nodejs/node_cache/gcc-ranlib-15.com"
      - "D:/nodejs/node_cache/gcc-ranlib-15.exe"
      - "D:/nodejs/node_cache/gcc-ranlib-15"
      - "D:/jdk/bin/gcc-ranlib-15.com"
      - "D:/jdk/bin/gcc-ranlib-15.exe"
      - "D:/jdk/bin/gcc-ranlib-15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15.com"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15.exe"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15"
      - "D:/qiuyv123/gcc-ranlib-15.com"
      - "D:/qiuyv123/gcc-ranlib-15.exe"
      - "D:/qiuyv123/gcc-ranlib-15"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15.com"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15.exe"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15"
      - "D:/src/vcpkg/gcc-ranlib-15.com"
      - "D:/src/vcpkg/gcc-ranlib-15.exe"
      - "D:/src/vcpkg/gcc-ranlib-15"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib15.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib15.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib15"
      - "D:/AAVM/software/usr/bin/gcc-ranlib15.com"
      - "D:/AAVM/software/usr/bin/gcc-ranlib15.exe"
      - "D:/AAVM/software/usr/bin/gcc-ranlib15"
      - "D:/visal/bin/gcc-ranlib15.com"
      - "D:/visal/bin/gcc-ranlib15.exe"
      - "D:/visal/bin/gcc-ranlib15"
      - "C:/Windows/System32/gcc-ranlib15.com"
      - "C:/Windows/System32/gcc-ranlib15.exe"
      - "C:/Windows/System32/gcc-ranlib15"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib15.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib15.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib15"
      - "D:/jdk/jre/bin/gcc-ranlib15.com"
      - "D:/jdk/jre/bin/gcc-ranlib15.exe"
      - "D:/jdk/jre/bin/gcc-ranlib15"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib15.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib15.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib15"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib15.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib15.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib15"
      - "D:/Program Files/Nox/bin/gcc-ranlib15.com"
      - "D:/Program Files/Nox/bin/gcc-ranlib15.exe"
      - "D:/Program Files/Nox/bin/gcc-ranlib15"
      - "D:/sdk/platform-tools/gcc-ranlib15.com"
      - "D:/sdk/platform-tools/gcc-ranlib15.exe"
      - "D:/sdk/platform-tools/gcc-ranlib15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib15"
      - "C:/Program Files/dotnet/gcc-ranlib15.com"
      - "C:/Program Files/dotnet/gcc-ranlib15.exe"
      - "C:/Program Files/dotnet/gcc-ranlib15"
      - "C:/Windows/gcc-ranlib15.com"
      - "C:/Windows/gcc-ranlib15.exe"
      - "C:/Windows/gcc-ranlib15"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15"
      - "C:/Windows/System32/wbem/gcc-ranlib15.com"
      - "C:/Windows/System32/wbem/gcc-ranlib15.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15"
      - "D:/nodejs/gcc-ranlib15.com"
      - "D:/nodejs/gcc-ranlib15.exe"
      - "D:/nodejs/gcc-ranlib15"
      - "D:/nodejs/node_global/gcc-ranlib15.com"
      - "D:/nodejs/node_global/gcc-ranlib15.exe"
      - "D:/nodejs/node_global/gcc-ranlib15"
      - "D:/nodejs/node_cache/gcc-ranlib15.com"
      - "D:/nodejs/node_cache/gcc-ranlib15.exe"
      - "D:/nodejs/node_cache/gcc-ranlib15"
      - "D:/jdk/bin/gcc-ranlib15.com"
      - "D:/jdk/bin/gcc-ranlib15.exe"
      - "D:/jdk/bin/gcc-ranlib15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib15.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib15.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib15"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib15.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib15.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib15"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib15.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib15.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib15"
      - "D:/qiuyv123/Scripts/gcc-ranlib15.com"
      - "D:/qiuyv123/Scripts/gcc-ranlib15.exe"
      - "D:/qiuyv123/Scripts/gcc-ranlib15"
      - "D:/qiuyv123/gcc-ranlib15.com"
      - "D:/qiuyv123/gcc-ranlib15.exe"
      - "D:/qiuyv123/gcc-ranlib15"
      - "D:/qiuyv123/Library/bin/gcc-ranlib15.com"
      - "D:/qiuyv123/Library/bin/gcc-ranlib15.exe"
      - "D:/qiuyv123/Library/bin/gcc-ranlib15"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib15.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib15.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib15"
      - "D:/src/vcpkg/gcc-ranlib15.com"
      - "D:/src/vcpkg/gcc-ranlib15.exe"
      - "D:/src/vcpkg/gcc-ranlib15"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib.com"
    found: "D:/AAVM/software/mingw64/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:462 (find_file)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:500 (CMAKE_DETERMINE_COMPILER_ID_WRITE)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:8 (CMAKE_DETERMINE_COMPILER_ID_BUILD)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    mode: "file"
    variable: "src_in"
    description: "Path to a file."
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "CMakeCXXCompilerId.cpp.in"
    candidate_directories:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/"
    found: "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCXXCompilerId.cpp.in"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:125 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: D:/AAVM/software/mingw64/bin/g++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is GNU, found in:
        D:/AAVM/Http_Server/build/CMakeFiles/4.1.0/CompilerIdCXX/a.exe
      
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:18 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_AR"
    description: "A wrapper around 'ar' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ar-15.1"
      - "gcc-ar-15"
      - "gcc-ar15"
      - "gcc-ar"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15.1.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15.1.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15.1"
      - "D:/AAVM/software/usr/bin/gcc-ar-15.1.com"
      - "D:/AAVM/software/usr/bin/gcc-ar-15.1.exe"
      - "D:/AAVM/software/usr/bin/gcc-ar-15.1"
      - "D:/visal/bin/gcc-ar-15.1.com"
      - "D:/visal/bin/gcc-ar-15.1.exe"
      - "D:/visal/bin/gcc-ar-15.1"
      - "C:/Windows/System32/gcc-ar-15.1.com"
      - "C:/Windows/System32/gcc-ar-15.1.exe"
      - "C:/Windows/System32/gcc-ar-15.1"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15.1.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15.1.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15.1"
      - "D:/jdk/jre/bin/gcc-ar-15.1.com"
      - "D:/jdk/jre/bin/gcc-ar-15.1.exe"
      - "D:/jdk/jre/bin/gcc-ar-15.1"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15.1.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15.1.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15.1"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15.1.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15.1.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15.1"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15.1.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15.1.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15.1"
      - "D:/Program Files/Nox/bin/gcc-ar-15.1.com"
      - "D:/Program Files/Nox/bin/gcc-ar-15.1.exe"
      - "D:/Program Files/Nox/bin/gcc-ar-15.1"
      - "D:/sdk/platform-tools/gcc-ar-15.1.com"
      - "D:/sdk/platform-tools/gcc-ar-15.1.exe"
      - "D:/sdk/platform-tools/gcc-ar-15.1"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15.1.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15.1.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15.1"
      - "C:/Program Files/dotnet/gcc-ar-15.1.com"
      - "C:/Program Files/dotnet/gcc-ar-15.1.exe"
      - "C:/Program Files/dotnet/gcc-ar-15.1"
      - "C:/Windows/gcc-ar-15.1.com"
      - "C:/Windows/gcc-ar-15.1.exe"
      - "C:/Windows/gcc-ar-15.1"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.1.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.1.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.1"
      - "C:/Windows/System32/wbem/gcc-ar-15.1.com"
      - "C:/Windows/System32/wbem/gcc-ar-15.1.exe"
      - "C:/Windows/System32/wbem/gcc-ar-15.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.1"
      - "D:/nodejs/gcc-ar-15.1.com"
      - "D:/nodejs/gcc-ar-15.1.exe"
      - "D:/nodejs/gcc-ar-15.1"
      - "D:/nodejs/node_global/gcc-ar-15.1.com"
      - "D:/nodejs/node_global/gcc-ar-15.1.exe"
      - "D:/nodejs/node_global/gcc-ar-15.1"
      - "D:/nodejs/node_cache/gcc-ar-15.1.com"
      - "D:/nodejs/node_cache/gcc-ar-15.1.exe"
      - "D:/nodejs/node_cache/gcc-ar-15.1"
      - "D:/jdk/bin/gcc-ar-15.1.com"
      - "D:/jdk/bin/gcc-ar-15.1.exe"
      - "D:/jdk/bin/gcc-ar-15.1"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15.1.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15.1.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15.1"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.1"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.1.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.1.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.1"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15.1.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15.1.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15.1"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15.1.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15.1.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15.1"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15.1.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15.1.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15.1"
      - "D:/qiuyv123/Scripts/gcc-ar-15.1.com"
      - "D:/qiuyv123/Scripts/gcc-ar-15.1.exe"
      - "D:/qiuyv123/Scripts/gcc-ar-15.1"
      - "D:/qiuyv123/gcc-ar-15.1.com"
      - "D:/qiuyv123/gcc-ar-15.1.exe"
      - "D:/qiuyv123/gcc-ar-15.1"
      - "D:/qiuyv123/Library/bin/gcc-ar-15.1.com"
      - "D:/qiuyv123/Library/bin/gcc-ar-15.1.exe"
      - "D:/qiuyv123/Library/bin/gcc-ar-15.1"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15.1.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15.1.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15.1"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15.1.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15.1.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15.1"
      - "D:/src/vcpkg/gcc-ar-15.1.com"
      - "D:/src/vcpkg/gcc-ar-15.1.exe"
      - "D:/src/vcpkg/gcc-ar-15.1"
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ar-15"
      - "D:/AAVM/software/usr/bin/gcc-ar-15.com"
      - "D:/AAVM/software/usr/bin/gcc-ar-15.exe"
      - "D:/AAVM/software/usr/bin/gcc-ar-15"
      - "D:/visal/bin/gcc-ar-15.com"
      - "D:/visal/bin/gcc-ar-15.exe"
      - "D:/visal/bin/gcc-ar-15"
      - "C:/Windows/System32/gcc-ar-15.com"
      - "C:/Windows/System32/gcc-ar-15.exe"
      - "C:/Windows/System32/gcc-ar-15"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar-15"
      - "D:/jdk/jre/bin/gcc-ar-15.com"
      - "D:/jdk/jre/bin/gcc-ar-15.exe"
      - "D:/jdk/jre/bin/gcc-ar-15"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar-15"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar-15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar-15"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar-15"
      - "D:/Program Files/Nox/bin/gcc-ar-15.com"
      - "D:/Program Files/Nox/bin/gcc-ar-15.exe"
      - "D:/Program Files/Nox/bin/gcc-ar-15"
      - "D:/sdk/platform-tools/gcc-ar-15.com"
      - "D:/sdk/platform-tools/gcc-ar-15.exe"
      - "D:/sdk/platform-tools/gcc-ar-15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar-15"
      - "C:/Program Files/dotnet/gcc-ar-15.com"
      - "C:/Program Files/dotnet/gcc-ar-15.exe"
      - "C:/Program Files/dotnet/gcc-ar-15"
      - "C:/Windows/gcc-ar-15.com"
      - "C:/Windows/gcc-ar-15.exe"
      - "C:/Windows/gcc-ar-15"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar-15"
      - "C:/Windows/System32/wbem/gcc-ar-15.com"
      - "C:/Windows/System32/wbem/gcc-ar-15.exe"
      - "C:/Windows/System32/wbem/gcc-ar-15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar-15"
      - "D:/nodejs/gcc-ar-15.com"
      - "D:/nodejs/gcc-ar-15.exe"
      - "D:/nodejs/gcc-ar-15"
      - "D:/nodejs/node_global/gcc-ar-15.com"
      - "D:/nodejs/node_global/gcc-ar-15.exe"
      - "D:/nodejs/node_global/gcc-ar-15"
      - "D:/nodejs/node_cache/gcc-ar-15.com"
      - "D:/nodejs/node_cache/gcc-ar-15.exe"
      - "D:/nodejs/node_cache/gcc-ar-15"
      - "D:/jdk/bin/gcc-ar-15.com"
      - "D:/jdk/bin/gcc-ar-15.exe"
      - "D:/jdk/bin/gcc-ar-15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar-15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar-15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar-15"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar-15"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar-15"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar-15"
      - "D:/qiuyv123/Scripts/gcc-ar-15.com"
      - "D:/qiuyv123/Scripts/gcc-ar-15.exe"
      - "D:/qiuyv123/Scripts/gcc-ar-15"
      - "D:/qiuyv123/gcc-ar-15.com"
      - "D:/qiuyv123/gcc-ar-15.exe"
      - "D:/qiuyv123/gcc-ar-15"
      - "D:/qiuyv123/Library/bin/gcc-ar-15.com"
      - "D:/qiuyv123/Library/bin/gcc-ar-15.exe"
      - "D:/qiuyv123/Library/bin/gcc-ar-15"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar-15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar-15"
      - "D:/src/vcpkg/gcc-ar-15.com"
      - "D:/src/vcpkg/gcc-ar-15.exe"
      - "D:/src/vcpkg/gcc-ar-15"
      - "D:/AAVM/software/mingw64/bin/gcc-ar15.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ar15.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ar15"
      - "D:/AAVM/software/usr/bin/gcc-ar15.com"
      - "D:/AAVM/software/usr/bin/gcc-ar15.exe"
      - "D:/AAVM/software/usr/bin/gcc-ar15"
      - "D:/visal/bin/gcc-ar15.com"
      - "D:/visal/bin/gcc-ar15.exe"
      - "D:/visal/bin/gcc-ar15"
      - "C:/Windows/System32/gcc-ar15.com"
      - "C:/Windows/System32/gcc-ar15.exe"
      - "C:/Windows/System32/gcc-ar15"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar15.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar15.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ar15"
      - "D:/jdk/jre/bin/gcc-ar15.com"
      - "D:/jdk/jre/bin/gcc-ar15.exe"
      - "D:/jdk/jre/bin/gcc-ar15"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar15.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar15.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ar15"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar15.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar15.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ar15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ar15"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar15.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar15.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ar15"
      - "D:/Program Files/Nox/bin/gcc-ar15.com"
      - "D:/Program Files/Nox/bin/gcc-ar15.exe"
      - "D:/Program Files/Nox/bin/gcc-ar15"
      - "D:/sdk/platform-tools/gcc-ar15.com"
      - "D:/sdk/platform-tools/gcc-ar15.exe"
      - "D:/sdk/platform-tools/gcc-ar15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ar15"
      - "C:/Program Files/dotnet/gcc-ar15.com"
      - "C:/Program Files/dotnet/gcc-ar15.exe"
      - "C:/Program Files/dotnet/gcc-ar15"
      - "C:/Windows/gcc-ar15.com"
      - "C:/Windows/gcc-ar15.exe"
      - "C:/Windows/gcc-ar15"
      - "C:/Windows/System32/OpenSSH/gcc-ar15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ar15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ar15"
      - "C:/Windows/System32/wbem/gcc-ar15.com"
      - "C:/Windows/System32/wbem/gcc-ar15.exe"
      - "C:/Windows/System32/wbem/gcc-ar15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ar15"
      - "D:/nodejs/gcc-ar15.com"
      - "D:/nodejs/gcc-ar15.exe"
      - "D:/nodejs/gcc-ar15"
      - "D:/nodejs/node_global/gcc-ar15.com"
      - "D:/nodejs/node_global/gcc-ar15.exe"
      - "D:/nodejs/node_global/gcc-ar15"
      - "D:/nodejs/node_cache/gcc-ar15.com"
      - "D:/nodejs/node_cache/gcc-ar15.exe"
      - "D:/nodejs/node_cache/gcc-ar15"
      - "D:/jdk/bin/gcc-ar15.com"
      - "D:/jdk/bin/gcc-ar15.exe"
      - "D:/jdk/bin/gcc-ar15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ar15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ar15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ar15"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar15.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar15.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ar15"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar15.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar15.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ar15"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar15.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar15.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ar15"
      - "D:/qiuyv123/Scripts/gcc-ar15.com"
      - "D:/qiuyv123/Scripts/gcc-ar15.exe"
      - "D:/qiuyv123/Scripts/gcc-ar15"
      - "D:/qiuyv123/gcc-ar15.com"
      - "D:/qiuyv123/gcc-ar15.exe"
      - "D:/qiuyv123/gcc-ar15"
      - "D:/qiuyv123/Library/bin/gcc-ar15.com"
      - "D:/qiuyv123/Library/bin/gcc-ar15.exe"
      - "D:/qiuyv123/Library/bin/gcc-ar15"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar15.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar15.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ar15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ar15"
      - "D:/src/vcpkg/gcc-ar15.com"
      - "D:/src/vcpkg/gcc-ar15.exe"
      - "D:/src/vcpkg/gcc-ar15"
      - "D:/AAVM/software/mingw64/bin/gcc-ar.com"
    found: "D:/AAVM/software/mingw64/bin/gcc-ar.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake:30 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake:207 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_CXX_COMPILER_RANLIB"
    description: "A wrapper around 'ranlib' adding the appropriate '--plugin' option for the GCC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: false
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: false
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "gcc-ranlib-15.1"
      - "gcc-ranlib-15"
      - "gcc-ranlib15"
      - "gcc-ranlib"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15.1.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15.1.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15.1"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15.1.com"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15.1.exe"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15.1"
      - "D:/visal/bin/gcc-ranlib-15.1.com"
      - "D:/visal/bin/gcc-ranlib-15.1.exe"
      - "D:/visal/bin/gcc-ranlib-15.1"
      - "C:/Windows/System32/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/gcc-ranlib-15.1"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15.1.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15.1.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15.1"
      - "D:/jdk/jre/bin/gcc-ranlib-15.1.com"
      - "D:/jdk/jre/bin/gcc-ranlib-15.1.exe"
      - "D:/jdk/jre/bin/gcc-ranlib-15.1"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15.1"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15.1.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15.1.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15.1"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15.1.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15.1.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15.1"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15.1.com"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15.1.exe"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15.1"
      - "D:/sdk/platform-tools/gcc-ranlib-15.1.com"
      - "D:/sdk/platform-tools/gcc-ranlib-15.1.exe"
      - "D:/sdk/platform-tools/gcc-ranlib-15.1"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15.1.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15.1.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15.1"
      - "C:/Program Files/dotnet/gcc-ranlib-15.1.com"
      - "C:/Program Files/dotnet/gcc-ranlib-15.1.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-15.1"
      - "C:/Windows/gcc-ranlib-15.1.com"
      - "C:/Windows/gcc-ranlib-15.1.exe"
      - "C:/Windows/gcc-ranlib-15.1"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.1"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.1"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.1"
      - "D:/nodejs/gcc-ranlib-15.1.com"
      - "D:/nodejs/gcc-ranlib-15.1.exe"
      - "D:/nodejs/gcc-ranlib-15.1"
      - "D:/nodejs/node_global/gcc-ranlib-15.1.com"
      - "D:/nodejs/node_global/gcc-ranlib-15.1.exe"
      - "D:/nodejs/node_global/gcc-ranlib-15.1"
      - "D:/nodejs/node_cache/gcc-ranlib-15.1.com"
      - "D:/nodejs/node_cache/gcc-ranlib-15.1.exe"
      - "D:/nodejs/node_cache/gcc-ranlib-15.1"
      - "D:/jdk/bin/gcc-ranlib-15.1.com"
      - "D:/jdk/bin/gcc-ranlib-15.1.exe"
      - "D:/jdk/bin/gcc-ranlib-15.1"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15.1.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15.1.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15.1"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.1"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.1.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.1.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.1"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15.1.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15.1.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15.1"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15.1.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15.1.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15.1"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15.1.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15.1.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15.1"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15.1.com"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15.1.exe"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15.1"
      - "D:/qiuyv123/gcc-ranlib-15.1.com"
      - "D:/qiuyv123/gcc-ranlib-15.1.exe"
      - "D:/qiuyv123/gcc-ranlib-15.1"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15.1.com"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15.1.exe"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15.1"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15.1.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15.1.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15.1"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15.1.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15.1.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15.1"
      - "D:/src/vcpkg/gcc-ranlib-15.1.com"
      - "D:/src/vcpkg/gcc-ranlib-15.1.exe"
      - "D:/src/vcpkg/gcc-ranlib-15.1"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib-15"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15.com"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15.exe"
      - "D:/AAVM/software/usr/bin/gcc-ranlib-15"
      - "D:/visal/bin/gcc-ranlib-15.com"
      - "D:/visal/bin/gcc-ranlib-15.exe"
      - "D:/visal/bin/gcc-ranlib-15"
      - "C:/Windows/System32/gcc-ranlib-15.com"
      - "C:/Windows/System32/gcc-ranlib-15.exe"
      - "C:/Windows/System32/gcc-ranlib-15"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib-15"
      - "D:/jdk/jre/bin/gcc-ranlib-15.com"
      - "D:/jdk/jre/bin/gcc-ranlib-15.exe"
      - "D:/jdk/jre/bin/gcc-ranlib-15"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib-15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib-15"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib-15"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15.com"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15.exe"
      - "D:/Program Files/Nox/bin/gcc-ranlib-15"
      - "D:/sdk/platform-tools/gcc-ranlib-15.com"
      - "D:/sdk/platform-tools/gcc-ranlib-15.exe"
      - "D:/sdk/platform-tools/gcc-ranlib-15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib-15"
      - "C:/Program Files/dotnet/gcc-ranlib-15.com"
      - "C:/Program Files/dotnet/gcc-ranlib-15.exe"
      - "C:/Program Files/dotnet/gcc-ranlib-15"
      - "C:/Windows/gcc-ranlib-15.com"
      - "C:/Windows/gcc-ranlib-15.exe"
      - "C:/Windows/gcc-ranlib-15"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib-15"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.com"
      - "C:/Windows/System32/wbem/gcc-ranlib-15.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib-15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib-15"
      - "D:/nodejs/gcc-ranlib-15.com"
      - "D:/nodejs/gcc-ranlib-15.exe"
      - "D:/nodejs/gcc-ranlib-15"
      - "D:/nodejs/node_global/gcc-ranlib-15.com"
      - "D:/nodejs/node_global/gcc-ranlib-15.exe"
      - "D:/nodejs/node_global/gcc-ranlib-15"
      - "D:/nodejs/node_cache/gcc-ranlib-15.com"
      - "D:/nodejs/node_cache/gcc-ranlib-15.exe"
      - "D:/nodejs/node_cache/gcc-ranlib-15"
      - "D:/jdk/bin/gcc-ranlib-15.com"
      - "D:/jdk/bin/gcc-ranlib-15.exe"
      - "D:/jdk/bin/gcc-ranlib-15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib-15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib-15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib-15"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib-15"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib-15"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib-15"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15.com"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15.exe"
      - "D:/qiuyv123/Scripts/gcc-ranlib-15"
      - "D:/qiuyv123/gcc-ranlib-15.com"
      - "D:/qiuyv123/gcc-ranlib-15.exe"
      - "D:/qiuyv123/gcc-ranlib-15"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15.com"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15.exe"
      - "D:/qiuyv123/Library/bin/gcc-ranlib-15"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib-15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib-15"
      - "D:/src/vcpkg/gcc-ranlib-15.com"
      - "D:/src/vcpkg/gcc-ranlib-15.exe"
      - "D:/src/vcpkg/gcc-ranlib-15"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib15.com"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib15.exe"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib15"
      - "D:/AAVM/software/usr/bin/gcc-ranlib15.com"
      - "D:/AAVM/software/usr/bin/gcc-ranlib15.exe"
      - "D:/AAVM/software/usr/bin/gcc-ranlib15"
      - "D:/visal/bin/gcc-ranlib15.com"
      - "D:/visal/bin/gcc-ranlib15.exe"
      - "D:/visal/bin/gcc-ranlib15"
      - "C:/Windows/System32/gcc-ranlib15.com"
      - "C:/Windows/System32/gcc-ranlib15.exe"
      - "C:/Windows/System32/gcc-ranlib15"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib15.com"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib15.exe"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/gcc-ranlib15"
      - "D:/jdk/jre/bin/gcc-ranlib15.com"
      - "D:/jdk/jre/bin/gcc-ranlib15.exe"
      - "D:/jdk/jre/bin/gcc-ranlib15"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.com"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.exe"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib15.com"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib15.exe"
      - "C:/Windows/System32/HWAudioDriver/gcc-ranlib15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/gcc-ranlib15"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib15.com"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib15.exe"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/gcc-ranlib15"
      - "D:/Program Files/Nox/bin/gcc-ranlib15.com"
      - "D:/Program Files/Nox/bin/gcc-ranlib15.exe"
      - "D:/Program Files/Nox/bin/gcc-ranlib15"
      - "D:/sdk/platform-tools/gcc-ranlib15.com"
      - "D:/sdk/platform-tools/gcc-ranlib15.exe"
      - "D:/sdk/platform-tools/gcc-ranlib15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/gcc-ranlib15"
      - "C:/Program Files/dotnet/gcc-ranlib15.com"
      - "C:/Program Files/dotnet/gcc-ranlib15.exe"
      - "C:/Program Files/dotnet/gcc-ranlib15"
      - "C:/Windows/gcc-ranlib15.com"
      - "C:/Windows/gcc-ranlib15.exe"
      - "C:/Windows/gcc-ranlib15"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15.com"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15.exe"
      - "C:/Windows/System32/OpenSSH/gcc-ranlib15"
      - "C:/Windows/System32/wbem/gcc-ranlib15.com"
      - "C:/Windows/System32/wbem/gcc-ranlib15.exe"
      - "C:/Windows/System32/wbem/gcc-ranlib15"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.com"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15.exe"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/gcc-ranlib15"
      - "D:/nodejs/gcc-ranlib15.com"
      - "D:/nodejs/gcc-ranlib15.exe"
      - "D:/nodejs/gcc-ranlib15"
      - "D:/nodejs/node_global/gcc-ranlib15.com"
      - "D:/nodejs/node_global/gcc-ranlib15.exe"
      - "D:/nodejs/node_global/gcc-ranlib15"
      - "D:/nodejs/node_cache/gcc-ranlib15.com"
      - "D:/nodejs/node_cache/gcc-ranlib15.exe"
      - "D:/nodejs/node_cache/gcc-ranlib15"
      - "D:/jdk/bin/gcc-ranlib15.com"
      - "D:/jdk/bin/gcc-ranlib15.exe"
      - "D:/jdk/bin/gcc-ranlib15"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib15.com"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib15.exe"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/gcc-ranlib15"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.com"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15.exe"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/gcc-ranlib15"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15.com"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15.exe"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/gcc-ranlib15"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib15.com"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib15.exe"
      - "C:/Users/<USER>/.dotnet/tools/gcc-ranlib15"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib15.com"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib15.exe"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/gcc-ranlib15"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib15.com"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib15.exe"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/gcc-ranlib15"
      - "D:/qiuyv123/Scripts/gcc-ranlib15.com"
      - "D:/qiuyv123/Scripts/gcc-ranlib15.exe"
      - "D:/qiuyv123/Scripts/gcc-ranlib15"
      - "D:/qiuyv123/gcc-ranlib15.com"
      - "D:/qiuyv123/gcc-ranlib15.exe"
      - "D:/qiuyv123/gcc-ranlib15"
      - "D:/qiuyv123/Library/bin/gcc-ranlib15.com"
      - "D:/qiuyv123/Library/bin/gcc-ranlib15.exe"
      - "D:/qiuyv123/Library/bin/gcc-ranlib15"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib15.com"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib15.exe"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/gcc-ranlib15"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib15.com"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib15.exe"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/gcc-ranlib15"
      - "D:/src/vcpkg/gcc-ranlib15.com"
      - "D:/src/vcpkg/gcc-ranlib15.exe"
      - "D:/src/vcpkg/gcc-ranlib15"
      - "D:/AAVM/software/mingw64/bin/gcc-ranlib.com"
    found: "D:/AAVM/software/mingw64/bin/gcc-ranlib.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
  -
    kind: "find-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake:40 (find_program)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake:167 (enable_language)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU-C.cmake:2 (__windows_compiler_gnu)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCInformation.cmake:48 (include)"
      - "CMakeLists.txt:4 (project)"
    mode: "program"
    variable: "CMAKE_RC_COMPILER"
    description: "RC compiler"
    settings:
      SearchFramework: "NEVER"
      SearchAppBundle: "NEVER"
      CMAKE_FIND_USE_CMAKE_PATH: true
      CMAKE_FIND_USE_CMAKE_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_SYSTEM_ENVIRONMENT_PATH: true
      CMAKE_FIND_USE_CMAKE_SYSTEM_PATH: true
      CMAKE_FIND_USE_INSTALL_PREFIX: true
    names:
      - "windres"
      - "windres"
    candidate_directories:
      - "D:/AAVM/software/mingw64/bin/"
      - "D:/AAVM/software/usr/bin/"
      - "D:/visal/bin/"
      - "C:/Windows/System32/"
      - "D:/AAVM/Http_Server/build/%JAVA_HOMEb%/bin/"
      - "D:/jdk/jre/bin/"
      - "C:/Windows/System32/config/systemprofile/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Windows/System32/HWAudioDriver/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin\uff1b/"
      - "D:/AAVM/Http_Server/build/:/Program Files/dotnet/"
      - "D:/Program Files/Nox/bin/"
      - "D:/sdk/platform-tools/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/cmd/"
      - "C:/Program Files/dotnet/"
      - "C:/Windows/"
      - "C:/Windows/System32/OpenSSH/"
      - "C:/Windows/System32/wbem/"
      - "C:/Windows/System32/WindowsPowerShell/v1.0/"
      - "D:/nodejs/"
      - "D:/nodejs/node_global/"
      - "D:/nodejs/node_cache/"
      - "D:/jdk/bin/"
      - "D:/ProgramData/MySQL/MySQL Server 8.0/bin/"
      - "C:/Users/<USER>/AppData/Local/Microsoft/WindowsApps/"
      - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin/"
      - "C:/Users/<USER>/.dotnet/tools/"
      - "D:/5EDemocache/PyCharm 2024.1.7/bin/"
      - "D:/asdasdadadasasdasasdasdasdasdasdasd/cursor/resources/app/bin/"
      - "D:/qiuyv123/Scripts/"
      - "D:/qiuyv123/"
      - "D:/qiuyv123/Library/bin/"
      - "D:/5EDemocache/PyCharm Community Edition 2025.1.1/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/bin/"
      - "D:/src/vcpkg/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/mingw64/bin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/mingw64/sbin/"
      - "D:/\u6709\u501f\u65e0\u575a/Git/mingw64/"
      - "C:/Program Files/bin/"
      - "C:/Program Files/sbin/"
      - "C:/Program Files/"
      - "C:/Program Files (x86)/bin/"
      - "C:/Program Files (x86)/sbin/"
      - "C:/Program Files (x86)/"
      - "D:/C++source/Tools/Cmake/bin/"
      - "D:/C++source/Tools/Cmake/sbin/"
      - "D:/C++source/Tools/Cmake/"
      - "C:/Program Files (x86)/Http_Server/bin/"
      - "C:/Program Files (x86)/Http_Server/sbin/"
      - "C:/Program Files (x86)/Http_Server/"
    searched_directories:
      - "D:/AAVM/software/mingw64/bin/windres.com"
    found: "D:/AAVM/software/mingw64/bin/windres.exe"
    search_context:
      ENV{PATH}:
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\mingw64\\bin"
        - "D:\\AAVM\\software\\usr\\bin"
        - "D:/visal/bin/"
        - "C:\\Windows\\System32"
        - "%JAVA_HOMEb%\\bin"
        - "D:/jdk/jre/bin"
        - "C:\\Windows\\system32\\config\\systemprofile\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/windows/system32/HWAudioDriver/"
        - "D:\\ProgramData\\MySQL\\MySQL Server 8.0\\bin\uff1b"
        - ":\\Program Files\\dotnet\\"
        - "D:/Program Files/Nox/bin"
        - "D:\\sdk\\platform-tools"
        - "D:/\u6709\u501f\u65e0\u575a/Git/cmd"
        - "C:\\Program Files\\dotnet\\"
        - "C:/Windows"
        - "C:\\Windows\\System32\\OpenSSH"
        - "C:/Windows/System32/Wbem"
        - "C:\\Windows\\System32\\WindowsPowerShell\\v1.0"
        - "D:/AAVM/software/mingw64/bin"
        - "D:\\nodejs\\"
        - "D:/nodejs"
        - "D:\\nodejs\\node_global"
        - "D:/nodejs/node_cache"
        - "D:\\jdk\\bin"
        - "D:/ProgramData/MySQL/MySQL Server 8.0/bin"
        - "C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps"
        - "C:/Users/<USER>/AppData/Local/Programs/Microsoft VS Code/bin"
        - "C:\\Users\\<USER>\\.dotnet\\tools"
        - "D:/5EDemocache/PyCharm 2024.1.7/bin"
        - "D:\\asdasdadadasasdasasdasdasdasdasdasd\\cursor\\resources\\app\\bin"
        - "D:/qiuyv123/Scripts"
        - "D:\\qiuyv123"
        - "D:/qiuyv123/Library/bin"
        - "D:\\5EDemocache\\PyCharm Community Edition 2025.1.1\\bin"
        - "D:/\u6709\u501f\u65e0\u575a/Git/bin"
        - "D:\\src\\vcpkg"
      CMAKE_INSTALL_PREFIX: "C:/Program Files (x86)/Http_Server"
      CMAKE_SYSTEM_PREFIX_PATH:
        - "D:/\u6709\u501f\u65e0\u575a/Git/mingw64"
        - "C:/Program Files"
        - "C:/Program Files (x86)"
        - "D:/C++source/Tools/Cmake"
        - "C:/Program Files (x86)/Http_Server"
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-tf72g5"
      binary: "D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-tf72g5"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-tf72g5'
        
        Run Build Command(s): D:/C++source/Tools/Cmake/bin/cmake.exe -E env VERBOSE=1 D:/AAVM/software/mingw64/bin/mingw32-make.exe -f Makefile cmTC_4730f/fast
        D:/AAVM/software/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_4730f.dir\\build.make CMakeFiles/cmTC_4730f.dir/build
        mingw32-make[1]: Entering directory 'D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-tf72g5'
        Building C object CMakeFiles/cmTC_4730f.dir/CMakeCCompilerABI.c.obj
        D:\\AAVM\\software\\mingw64\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.obj -c D:\\C++source\\Tools\\Cmake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c
        Using built-in specs.
        COLLECT_GCC=D:\\AAVM\\software\\mingw64\\bin\\gcc.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: win32
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4730f.dir\\'
         D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1.exe -quiet -v -iprefix D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -U_REENTRANT D:\\C++source\\Tools\\Cmake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_4730f.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccH7sMZv.s
        GNU C23 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) version 15.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.1.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include"
        ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"
        ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include
         D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed
         D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: ed56d56801e56e2208b774546f555c3f
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4730f.dir\\'
         D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccH7sMZv.s
        GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44
        COMPILER_PATH=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;D:/AAVM/software/mingw64/bin/../libexec/gcc/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;D:/AAVM/software/mingw64/bin/../lib/gcc/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.'
        Linking C executable cmTC_4730f.exe
        D:\\C++source\\Tools\\Cmake\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_4730f.dir\\link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=D:\\AAVM\\software\\mingw64\\bin\\gcc.exe
        COLLECT_LTO_WRAPPER=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: win32
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) 
        COMPILER_PATH=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;D:/AAVM/software/mingw64/bin/../libexec/gcc/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;D:/AAVM/software/mingw64/bin/../lib/gcc/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_4730f.exe' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_4730f.'
         D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cchInutu.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_4730f.exe D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LD:/AAVM/software/mingw64/bin/../lib/gcc -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_4730f.dir/objects.a --no-whole-archive --out-implib libcmTC_4730f.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        collect2 version 15.1.0
        D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cchInutu.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_4730f.exe D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LD:/AAVM/software/mingw64/bin/../lib/gcc -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_4730f.dir/objects.a --no-whole-archive --out-implib libcmTC_4730f.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        GNU ld (GNU Binutils) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_4730f.exe' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_4730f.'
        D:\\C++source\\Tools\\Cmake\\bin\\cmake.exe -E rm -f CMakeFiles\\cmTC_4730f.dir/objects.a
        D:\\AAVM\\software\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_4730f.dir/objects.a @CMakeFiles\\cmTC_4730f.dir\\objects1.rsp
        D:\\AAVM\\software\\mingw64\\bin\\gcc.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_4730f.dir/objects.a -Wl,--no-whole-archive -o cmTC_4730f.exe -Wl,--out-implib,libcmTC_4730f.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        mingw32-make[1]: Leaving directory 'D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-tf72g5'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed C implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
          add: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
          add: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        collapse include dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        collapse include dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include] ==> [D:/AAVM/software/mingw64/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include;D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed;D:/AAVM/software/mingw64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-tf72g5']
        ignore line: []
        ignore line: [Run Build Command(s): D:/C++source/Tools/Cmake/bin/cmake.exe -E env VERBOSE=1 D:/AAVM/software/mingw64/bin/mingw32-make.exe -f Makefile cmTC_4730f/fast]
        ignore line: [D:/AAVM/software/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_4730f.dir\\build.make CMakeFiles/cmTC_4730f.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-tf72g5']
        ignore line: [Building C object CMakeFiles/cmTC_4730f.dir/CMakeCCompilerABI.c.obj]
        ignore line: [D:\\AAVM\\software\\mingw64\\bin\\gcc.exe   -v -o CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.obj -c D:\\C++source\\Tools\\Cmake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\AAVM\\software\\mingw64\\bin\\gcc.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: win32]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4730f.dir\\']
        ignore line: [ D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1.exe -quiet -v -iprefix D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -U_REENTRANT D:\\C++source\\Tools\\Cmake\\share\\cmake-4.1\\Modules\\CMakeCCompilerABI.c -quiet -dumpdir CMakeFiles\\cmTC_4730f.dir\\ -dumpbase CMakeCCompilerABI.c.c -dumpbase-ext .c -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccH7sMZv.s]
        ignore line: [GNU C23 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) version 15.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.1.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include"]
        ignore line: [ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        ignore line: [ D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        ignore line: [ D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: ed56d56801e56e2208b774546f555c3f]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4730f.dir\\']
        ignore line: [ D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccH7sMZv.s]
        ignore line: [GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44]
        ignore line: [COMPILER_PATH=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/AAVM/software/mingw64/bin/../libexec/gcc/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.obj' '-c' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_4730f.dir\\CMakeCCompilerABI.c.']
        ignore line: [Linking C executable cmTC_4730f.exe]
        ignore line: [D:\\C++source\\Tools\\Cmake\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_4730f.dir\\link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\AAVM\\software\\mingw64\\bin\\gcc.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: win32]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COMPILER_PATH=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/AAVM/software/mingw64/bin/../libexec/gcc/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_4730f.exe' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_4730f.']
        link line: [ D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cchInutu.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_4730f.exe D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LD:/AAVM/software/mingw64/bin/../lib/gcc -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_4730f.dir/objects.a --no-whole-archive --out-implib libcmTC_4730f.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmingwex -lmsvcrt -lkernel32 D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\cchInutu.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-liconv] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [--sysroot=C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_4730f.exe] ==> ignore
          arg [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_4730f.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_4730f.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-liconv] ==> lib [iconv]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc] ==> lib [gcc]
          arg [-lgcc_eh] ==> lib [gcc_eh]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        linker tool for 'C': ../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe
        remove lib [gcc_eh]
        remove lib [msvcrt]
        remove lib [gcc_eh]
        remove lib [msvcrt]
        collapse obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [D:/AAVM/software/mingw64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
        collapse obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc] ==> [D:/AAVM/software/mingw64/lib/gcc]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [D:/AAVM/software/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [D:/AAVM/software/mingw64/lib]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> [D:/AAVM/software/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [D:/AAVM/software/mingw64/lib]
        implicit libs: [mingw32;gcc;mingwex;kernel32;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc;mingwex;kernel32]
        implicit objs: [D:/AAVM/software/mingw64/x86_64-w64-mingw32/lib/crt2.o;D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o;D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        implicit dirs: [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0;D:/AAVM/software/mingw64/lib/gcc;D:/AAVM/software/mingw64/x86_64-w64-mingw32/lib;D:/AAVM/software/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Running the C compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "-v"
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Running the C compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "-V"
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Running the C compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "--version"
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:83 (try_compile)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-zxt13v"
      binary: "D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-zxt13v"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-zxt13v'
        
        Run Build Command(s): D:/C++source/Tools/Cmake/bin/cmake.exe -E env VERBOSE=1 D:/AAVM/software/mingw64/bin/mingw32-make.exe -f Makefile cmTC_95df2/fast
        D:/AAVM/software/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_95df2.dir\\build.make CMakeFiles/cmTC_95df2.dir/build
        mingw32-make[1]: Entering directory 'D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-zxt13v'
        Building CXX object CMakeFiles/cmTC_95df2.dir/CMakeCXXCompilerABI.cpp.obj
        D:\\AAVM\\software\\mingw64\\bin\\g++.exe   -v -o CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.obj -c D:\\C++source\\Tools\\Cmake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp
        Using built-in specs.
        COLLECT_GCC=D:\\AAVM\\software\\mingw64\\bin\\g++.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: win32
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) 
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_95df2.dir\\'
         D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1plus.exe -quiet -v -iprefix D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -U_REENTRANT D:\\C++source\\Tools\\Cmake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_95df2.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKOJISg.s
        GNU C++17 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) version 15.1.0 (x86_64-w64-mingw32)
        	compiled by GNU C version 15.1.0, GMP version 6.2.1, MPFR version 4.1.0, MPC version 1.2.1, isl version isl-0.27-GMP
        
        GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072
        ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++"
        ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32"
        ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward"
        ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include"
        ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"
        ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"
        ignoring nonexistent directory "C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/mingw/include"
        #include "..." search starts here:
        #include <...> search starts here:
         D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++
         D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32
         D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward
         D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include
         D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed
         D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include
        End of search list.
        Compiler executable checksum: be9018a1d629d854d6ed8b0913c59dec
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_95df2.dir\\'
         D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKOJISg.s
        GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44
        COMPILER_PATH=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;D:/AAVM/software/mingw64/bin/../libexec/gcc/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;D:/AAVM/software/mingw64/bin/../lib/gcc/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.'
        Linking CXX executable cmTC_95df2.exe
        D:\\C++source\\Tools\\Cmake\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_95df2.dir\\link.txt --verbose=1
        Using built-in specs.
        COLLECT_GCC=D:\\AAVM\\software\\mingw64\\bin\\g++.exe
        COLLECT_LTO_WRAPPER=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe
        Target: x86_64-w64-mingw32
        Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc'
        Thread model: win32
        Supported LTO compression algorithms: zlib
        gcc version 15.1.0 (x86_64-win32-seh-rev0, Built by MinGW-Builds project) 
        COMPILER_PATH=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/;D:/AAVM/software/mingw64/bin/../libexec/gcc/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/
        LIBRARY_PATH=D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/;D:/AAVM/software/mingw64/bin/../lib/gcc/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/;D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_95df2.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_95df2.'
         D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccvJLbEQ.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_95df2.exe D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LD:/AAVM/software/mingw64/bin/../lib/gcc -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_95df2.dir/objects.a --no-whole-archive --out-implib libcmTC_95df2.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        collect2 version 15.1.0
        D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/ld.exe -plugin D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccvJLbEQ.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_95df2.exe D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LD:/AAVM/software/mingw64/bin/../lib/gcc -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_95df2.dir/objects.a --no-whole-archive --out-implib libcmTC_95df2.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o
        GNU ld (GNU Binutils) 2.44
        COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_95df2.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_95df2.'
        D:\\C++source\\Tools\\Cmake\\bin\\cmake.exe -E rm -f CMakeFiles\\cmTC_95df2.dir/objects.a
        D:\\AAVM\\software\\mingw64\\bin\\ar.exe qc CMakeFiles\\cmTC_95df2.dir/objects.a @CMakeFiles\\cmTC_95df2.dir\\objects1.rsp
        D:\\AAVM\\software\\mingw64\\bin\\g++.exe  -v -Wl,-v -Wl,--whole-archive CMakeFiles\\cmTC_95df2.dir/objects.a -Wl,--no-whole-archive -o cmTC_95df2.exe -Wl,--out-implib,libcmTC_95df2.dll.a -Wl,--major-image-version,0,--minor-image-version,0
        mingw32-make[1]: Leaving directory 'D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-zxt13v'
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:217 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed CXX implicit include dir info: rv=done
        found start of include info
        found start of implicit include info
          add: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++]
          add: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32]
          add: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward]
          add: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
          add: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
          add: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        end of search list found
        collapse include dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++]
        collapse include dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32]
        collapse include dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward]
        collapse include dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        collapse include dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        collapse include dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include] ==> [D:/AAVM/software/mingw64/x86_64-w64-mingw32/include]
        implicit include dirs: [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++;D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32;D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward;D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include;D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed;D:/AAVM/software/mingw64/x86_64-w64-mingw32/include]
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:253 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(|\\.[a-rt-z][a-z]*|\\.s[a-np-z][a-z]*|\\.so[a-z]+)))("|,| |$)]
        ignore line: [Change Dir: 'D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-zxt13v']
        ignore line: []
        ignore line: [Run Build Command(s): D:/C++source/Tools/Cmake/bin/cmake.exe -E env VERBOSE=1 D:/AAVM/software/mingw64/bin/mingw32-make.exe -f Makefile cmTC_95df2/fast]
        ignore line: [D:/AAVM/software/mingw64/bin/mingw32-make.exe  -f CMakeFiles\\cmTC_95df2.dir\\build.make CMakeFiles/cmTC_95df2.dir/build]
        ignore line: [mingw32-make[1]: Entering directory 'D:/AAVM/Http_Server/build/CMakeFiles/CMakeScratch/TryCompile-zxt13v']
        ignore line: [Building CXX object CMakeFiles/cmTC_95df2.dir/CMakeCXXCompilerABI.cpp.obj]
        ignore line: [D:\\AAVM\\software\\mingw64\\bin\\g++.exe   -v -o CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.obj -c D:\\C++source\\Tools\\Cmake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\AAVM\\software\\mingw64\\bin\\g++.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: win32]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_95df2.dir\\']
        ignore line: [ D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/cc1plus.exe -quiet -v -iprefix D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/ -U_REENTRANT D:\\C++source\\Tools\\Cmake\\share\\cmake-4.1\\Modules\\CMakeCXXCompilerABI.cpp -quiet -dumpdir CMakeFiles\\cmTC_95df2.dir\\ -dumpbase CMakeCXXCompilerABI.cpp.cpp -dumpbase-ext .cpp -mtune=core2 -march=nocona -version -o C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKOJISg.s]
        ignore line: [GNU C++17 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) version 15.1.0 (x86_64-w64-mingw32)]
        ignore line: [	compiled by GNU C version 15.1.0  GMP version 6.2.1  MPFR version 4.1.0  MPC version 1.2.1  isl version isl-0.27-GMP]
        ignore line: []
        ignore line: [GGC heuristics: --param ggc-min-expand=100 --param ggc-min-heapsize=131072]
        ignore line: [ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++"]
        ignore line: [ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32"]
        ignore line: [ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward"]
        ignore line: [ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64D:/a/_temp/msys64/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../include"]
        ignore line: [ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed"]
        ignore line: [ignoring duplicate directory "D:/AAVM/software/mingw64/lib/gcc/../../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include"]
        ignore line: [ignoring nonexistent directory "C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/mingw/include"]
        ignore line: [#include "..." search starts here:]
        ignore line: [#include <...> search starts here:]
        ignore line: [ D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++]
        ignore line: [ D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32]
        ignore line: [ D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward]
        ignore line: [ D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include]
        ignore line: [ D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed]
        ignore line: [ D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/include]
        ignore line: [End of search list.]
        ignore line: [Compiler executable checksum: be9018a1d629d854d6ed8b0913c59dec]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_95df2.dir\\']
        ignore line: [ D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/as.exe -v -o CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.obj C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccKOJISg.s]
        ignore line: [GNU assembler version 2.44 (x86_64-w64-mingw32) using BFD version (GNU Binutils) 2.44]
        ignore line: [COMPILER_PATH=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/AAVM/software/mingw64/bin/../libexec/gcc/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.obj' '-c' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'CMakeFiles\\cmTC_95df2.dir\\CMakeCXXCompilerABI.cpp.']
        ignore line: [Linking CXX executable cmTC_95df2.exe]
        ignore line: [D:\\C++source\\Tools\\Cmake\\bin\\cmake.exe -E cmake_link_script CMakeFiles\\cmTC_95df2.dir\\link.txt --verbose=1]
        ignore line: [Using built-in specs.]
        ignore line: [COLLECT_GCC=D:\\AAVM\\software\\mingw64\\bin\\g++.exe]
        ignore line: [COLLECT_LTO_WRAPPER=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe]
        ignore line: [Target: x86_64-w64-mingw32]
        ignore line: [Configured with: ../../../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https://github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe --with-boot-ldflags='-pipe -fno-ident -L/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/opt/lib -L/c/buildroot/prerequisites/x86_64-zlib-static/lib -L/c/buildroot/prerequisites/x86_64-w64-mingw32-static/lib  -Wl,--disable-dynamicbase -static-libstdc++ -static-libgcc']
        ignore line: [Thread model: win32]
        ignore line: [Supported LTO compression algorithms: zlib]
        ignore line: [gcc version 15.1.0 (x86_64-win32-seh-rev0  Built by MinGW-Builds project) ]
        ignore line: [COMPILER_PATH=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/AAVM/software/mingw64/bin/../libexec/gcc/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/bin/]
        ignore line: [LIBRARY_PATH=D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/]
        ignore line: [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../]
        ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_95df2.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona' '-dumpdir' 'cmTC_95df2.']
        link line: [ D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe -plugin D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll -plugin-opt=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccvJLbEQ.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lkernel32 --sysroot=C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_95df2.exe D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0 -LD:/AAVM/software/mingw64/bin/../lib/gcc -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib -LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../.. -v --whole-archive CMakeFiles\\cmTC_95df2.dir/objects.a --no-whole-archive --out-implib libcmTC_95df2.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmingwex -lmsvcrt -lkernel32 D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
          arg [D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/collect2.exe] ==> ignore
          arg [-plugin] ==> ignore
          arg [D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/liblto_plugin.dll] ==> ignore
          arg [-plugin-opt=D:/AAVM/software/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/15.1.0/lto-wrapper.exe] ==> ignore
          arg [-plugin-opt=-fresolution=C:\\Users\\<USER>\\AppData\\Local\\Temp\\ccvJLbEQ.res] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
          arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
          arg [-plugin-opt=-pass-through=-luser32] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [-plugin-opt=-pass-through=-liconv] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
          arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
          arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
          arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
          arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
          arg [--sysroot=C:/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64] ==> ignore
          arg [-m] ==> ignore
          arg [i386pep] ==> ignore
          arg [-Bdynamic] ==> search dynamic
          arg [-o] ==> ignore
          arg [cmTC_95df2.exe] ==> ignore
          arg [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o]
          arg [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib]
          arg [-LD:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..]
          arg [-v] ==> ignore
          arg [--whole-archive] ==> ignore
          arg [CMakeFiles\\cmTC_95df2.dir/objects.a] ==> ignore
          arg [--no-whole-archive] ==> ignore
          arg [--out-implib] ==> ignore
          arg [libcmTC_95df2.dll.a] ==> ignore
          arg [--major-image-version] ==> ignore
          arg [0] ==> ignore
          arg [--minor-image-version] ==> ignore
          arg [0] ==> ignore
          arg [-lstdc++] ==> lib [stdc++]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-ladvapi32] ==> lib [advapi32]
          arg [-lshell32] ==> lib [shell32]
          arg [-luser32] ==> lib [user32]
          arg [-lkernel32] ==> lib [kernel32]
          arg [-liconv] ==> lib [iconv]
          arg [-lmingw32] ==> lib [mingw32]
          arg [-lgcc_s] ==> lib [gcc_s]
          arg [-lgcc] ==> lib [gcc]
          arg [-lmingwex] ==> lib [mingwex]
          arg [-lmsvcrt] ==> lib [msvcrt]
          arg [-lkernel32] ==> lib [kernel32]
          arg [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        linker tool for 'CXX': ../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe
        remove lib [msvcrt]
        remove lib [msvcrt]
        collapse obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> [D:/AAVM/software/mingw64/x86_64-w64-mingw32/lib/crt2.o]
        collapse obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o]
        collapse obj [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0] ==> [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc] ==> [D:/AAVM/software/mingw64/lib/gcc]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [D:/AAVM/software/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../lib] ==> [D:/AAVM/software/mingw64/lib]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../../../x86_64-w64-mingw32/lib] ==> [D:/AAVM/software/mingw64/x86_64-w64-mingw32/lib]
        collapse library dir [D:/AAVM/software/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/15.1.0/../../..] ==> [D:/AAVM/software/mingw64/lib]
        implicit libs: [stdc++;mingw32;gcc_s;gcc;mingwex;kernel32;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc_s;gcc;mingwex;kernel32]
        implicit objs: [D:/AAVM/software/mingw64/x86_64-w64-mingw32/lib/crt2.o;D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtbegin.o;D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/crtend.o]
        implicit dirs: [D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0;D:/AAVM/software/mingw64/lib/gcc;D:/AAVM/software/mingw64/x86_64-w64-mingw32/lib;D:/AAVM/software/mingw64/lib]
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Running the CXX compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "-v"
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Running the CXX compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "-V"
      
  -
    kind: "message-v1"
    backtrace:
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake:36 (message)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake:299 (cmake_determine_linker_id)"
      - "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:4 (project)"
    message: |
      Running the CXX compiler's linker: "../src/gcc-15.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64 --enable-host-shared --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=win32 --enable-libstdcxx-threads=yes --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-mingw-wildcard --enable-fully-dynamic-string --enable-version-specific-runtime-libs --enable-libstdcxx-filesystem-ts=yes --disable-libssp --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/buildroot/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-win32-seh-rev0, Built by MinGW-Builds project' --with-bugurl=https:/github.com/niXman/mingw-builds LD_FOR_TARGET=/c/buildroot/x86_64-1510-win32-seh-ucrt-rt_v12-rev0/mingw64/bin/ld.exe" "--version"
      
...
