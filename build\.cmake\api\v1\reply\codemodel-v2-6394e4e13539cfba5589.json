{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-5247dc3e06e032c091dd.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Http_Server", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "XTcp::@6890427a1f51a3e7e1df", "jsonFile": "target-XTcp-Debug-48fe8118caef0df14b48.json", "name": "XTcp", "projectIndex": 0}, {"directoryIndex": 0, "id": "testsocket::@6890427a1f51a3e7e1df", "jsonFile": "target-testsocket-Debug-410d7a105b4ccb7a6183.json", "name": "testsocket", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/AAVM/Http_Server/build", "source": "D:/AAVM/Http_Server"}, "version": {"major": 2, "minor": 8}}